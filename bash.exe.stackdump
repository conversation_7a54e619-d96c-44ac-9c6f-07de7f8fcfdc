Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD18940000 ntdll.dll
7FFD17370000 KERNEL32.DLL
7FFD15EB0000 KERNELBASE.dll
7FFD17BA0000 USER32.dll
7FFD164B0000 win32u.dll
7FFD16EF0000 GDI32.dll
7FFD15BF0000 gdi32full.dll
7FFD164E0000 msvcp_win.dll
7FFD16590000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD17270000 advapi32.dll
7FFD171A0000 msvcrt.dll
7FFD18670000 sechost.dll
7FFD18550000 RPCRT4.dll
7FFD14FA0000 CRYPTBASE.DLL
7FFD15B50000 bcryptPrimitives.dll
7FFD17330000 IMM32.DLL
