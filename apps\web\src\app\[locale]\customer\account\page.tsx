'use client'
import { useCallback, useEffect, useState } from 'react'
import {
  Customer,
  resolveCatchMessage,
  TRACK_EVENT,
  useLazyGetAccountCenterInfoQuery,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'

import { OrderList } from '@/businessComponents'
import { Skeleton, UserProfile } from '@/components'

export default function AccountPage() {
  const toast = useToastContext()
  const { reportEvent } = useVolcAnalytics()

  const [loading, setLoading] = useState({
    customerLoading: true,
    orderStatusLoading: true,
    ordersLoading: true,
  })
  const [getAccountCenterInfo] = useLazyGetAccountCenterInfoQuery()
  const [customer, setCustomer] = useState<Customer>({
    info: {
      email: '',
      name: '',
      phone: '',
      nCoin: 0,
      avatar: '',
    },
    orders: {
      pending: '0',
      processing: '0',
      shipped: '0',
      complete: '0',
    },
    services: {
      coupons: [],
    },
  })

  const getAccountInfo = useCallback(() => {
    getAccountCenterInfo({})
      .unwrap()
      .then((res) => {
        const accountInfo = res
        if (accountInfo?.customer) {
          const currentCustomer = accountInfo.customer
          const currentCouponsData = accountInfo.customer_coupons.items
          setCustomer((prevState) => ({
            ...prevState,
            info: {
              email: currentCustomer?.customer_email,
              name: currentCustomer?.customer_info?.customer_nickname,
              phone: currentCustomer?.customer_phone,
              nCoin: currentCustomer?.customer_ncoin,
              avatar: currentCustomer?.customer_info?.customer_logo,
            },
            orders: accountInfo.order,
            services: {
              coupons: currentCouponsData,
            },
          }))
          setLoading((pre) => ({
            ...pre,
            customerLoading: false,
          }))
        }
      })
      .catch((error) => {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        })
      })
  }, [getAccountCenterInfo, toast])

  useEffect(() => {
    getAccountInfo()
  }, [getAccountInfo])

  /**
   * 埋点：点击个人中心
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_profile_page_exposure)
  }, [reportEvent])

  return (
    <div className="flex-1">
      {loading.customerLoading ? (
        <div className="mb-base-16 flex h-[327px] flex-col rounded-[20px] bg-white p-base-32">
          <div className="mb-[48px] flex h-[117px] w-full flex-row items-center justify-between">
            <Skeleton shape="circle" style={{ width: 108, height: 108 }} />
            <Skeleton style={{ width: '40%', height: '100%' }} />
            <Skeleton style={{ width: '33%' }} />
          </div>
          <div className="flex h-[170px] flex-row items-center justify-between">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} style={{ width: '20%' }} />
            ))}
          </div>
        </div>
      ) : (
        <UserProfile customer={customer} />
      )}

      <OrderList isPage={false} externalStatus="" />
    </div>
  )
}
