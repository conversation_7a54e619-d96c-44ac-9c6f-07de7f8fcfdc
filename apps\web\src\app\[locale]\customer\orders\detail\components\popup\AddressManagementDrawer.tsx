'use client'

import { useEffect, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  type Address,
  LocalToastProvider,
  selectUserAllAddress,
  setReturnAddressId,
  setUserAllAddress,
  useDebounceFn,
  useLocal<PERSON>oastContext,
  userOrderReturnAddressIdSelector,
  useUserAddress,
} from '@ninebot/core'
import { CustomerShippingAddressInput } from '@ninebot/core/src/graphql/generated/graphql'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd'

import { CustomDrawer, Location } from '@/components'

import { AddressEditDrawer } from './index'

interface AddressManagementDrawerProps {
  visible: boolean
  onClose: () => void
  onOpen?: () => void
}

const AddIcon = () => {
  return (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M-5.61958e-08 5.33463L0 6.66797L4.66667 6.66797L5.33333 6.0013L4.66667 5.33464L-5.61958e-08 5.33463Z"
        fill="#0F0F0F"
      />
      <path
        d="M5.33317 12L6.6665 12L6.6665 7.33333L5.99984 6.66667L5.33317 7.33333L5.33317 12Z"
        fill="#0F0F0F"
      />
      <path
        d="M12 5.33463L12 6.66797L7.33333 6.66797L6.66667 6.0013L7.33333 5.33464L12 5.33463Z"
        fill="#0F0F0F"
      />
      <path
        d="M6.66683 2.33127e-07L5.3335 0L5.3335 4.66667L6.00016 5.33333L6.66683 4.66667L6.66683 2.33127e-07Z"
        fill="#0F0F0F"
      />
    </svg>
  )
}

// 内部组件，使用 LocalToastProvider 的上下文
function AddressManagementContent({ visible, onClose, onOpen }: AddressManagementDrawerProps) {
  const getI18nString = useTranslations('Common')
  const toast = useLocalToastContext()
  const dispatch = useAppDispatch()
  const containerRef = useRef<HTMLDivElement>(null)

  const { fetchUserAddresses, updateUserAddress, addUserAddress } = useUserAddress()
  const userAllAddress = useAppSelector(selectUserAllAddress)
  const selectedAddressId = useAppSelector(userOrderReturnAddressIdSelector)

  // 地址弹窗相关状态
  const [isAddressModalOpen, setIsAddressModalOpen] = useState(false)
  const [editingAddress, setEditingAddress] = useState<Address | null>(null)
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  // const [addressToDelete, setAddressToDelete] = useState<Address | null>(null)

  // 临时选中的地址ID（未确认）
  const [tempSelectedAddressId, setTempSelectedAddressId] = useState<string | null>(
    selectedAddressId,
  )

  /**
   * 获取地址数据
   */
  useEffect(() => {
    if (visible) {
      fetchUserAddresses()
    }
  }, [fetchUserAddresses, visible])

  /**
   * 同步当前选中的地址ID到临时状态，如果没有选中地址则选中默认地址
   */
  useEffect(() => {
    if (visible && userAllAddress.length > 0) {
      // 检查当前选中的地址是否在地址列表中存在
      const selectedAddressExists =
        selectedAddressId &&
        userAllAddress.some((address) => String(address.address_id) === selectedAddressId)

      if (selectedAddressExists) {
        // 如果有选中的地址且存在于列表中，使用选中的地址
        setTempSelectedAddressId(selectedAddressId)
      } else {
        // 如果没有选中地址或选中的地址不存在，自动选中默认地址
        const defaultAddress = userAllAddress.find((address) => address.is_default === true)

        if (defaultAddress && defaultAddress.address_id) {
          setTempSelectedAddressId(String(defaultAddress.address_id))
        } else if (userAllAddress[0]?.address_id) {
          // 如果没有默认地址，选中第一个地址
          setTempSelectedAddressId(String(userAllAddress[0].address_id))
        }
      }
    }
  }, [visible, selectedAddressId, userAllAddress])

  /**
   * 弹窗打开时重置临时状态
   */
  useEffect(() => {
    if (visible) {
      setTempSelectedAddressId(selectedAddressId)
    }
  }, [visible, selectedAddressId])

  /**
   * 选择地址（仅更新临时状态）
   */
  const handleSelectAddress = (address: Address) => {
    setTempSelectedAddressId(address.address_id ? String(address.address_id) : null)
  }

  /**
   * 确认选择地址
   */
  const handleConfirmAddress = () => {
    dispatch(setReturnAddressId(tempSelectedAddressId))
    onClose()
  }

  /**
   * 打开添加地址弹窗
   */
  const handleOpenAddModal = () => {
    setEditingAddress(null)
    // 先关闭当前抽屉
    onClose()
    // 添加延迟，确保当前抽屉完全关闭后再打开新抽屉
    setTimeout(() => {
      setIsAddressModalOpen(true)
    }, 300)
  }

  /**
   * 打开编辑地址弹窗
   */
  // const handleOpenEditModal = (address: Address) => {
  //   setEditingAddress(address)
  //   // 先关闭当前抽屉
  //   onClose()
  //   // 添加延迟，确保当前抽屉完全关闭后再打开新抽屉
  //   setTimeout(() => {
  //     setIsAddressModalOpen(true)
  //   }, 300)
  // }

  /**
   * 关闭地址弹窗
   */
  const handleCloseAddressModal = () => {
    setIsAddressModalOpen(false)
    setEditingAddress(null)
  }

  /**
   * 添加地址
   */
  const { run: handleAddAddress } = useDebounceFn(async (values: Address) => {
    const result = await addUserAddress(values as CustomerShippingAddressInput)
    if (result) {
      toast.show({
        content: getI18nString('add_address_success'),
        icon: 'success',
      })
      dispatch(setUserAllAddress(result))
      handleCloseAddressModal()
      // 延迟重新打开地址管理抽屉
      setTimeout(() => {
        if (onOpen) {
          onOpen()
        }
      }, 300)
    }
  })

  /**
   * 更新地址
   */
  const { run: handleUpdateAddress } = useDebounceFn(async (values: Address) => {
    if (!editingAddress) return

    const result = await updateUserAddress(values as CustomerShippingAddressInput)
    if (result) {
      toast.show({
        content: getI18nString('update_address_success'),
        icon: 'success',
      })
      dispatch(setUserAllAddress(result))
      handleCloseAddressModal()
      // 延迟重新打开地址管理抽屉
      setTimeout(() => {
        if (onOpen) {
          onOpen()
        }
      }, 300)
    }
  })

  /**
   * 打开删除确认弹窗
   */
  // const handleOpenDeleteModal = (address: Address) => {
  //   setAddressToDelete(address)
  //   setIsDeleteModalOpen(true)
  // }

  /**
   * 确认删除地址
   */
  // const { run: handleConfirmDelete } = useDebounceFn(async () => {
  //   if (!addressToDelete) return

  //   const result = await deleteUserAddress(Number(addressToDelete.address_id))
  //   if (result && Array.isArray(result)) {
  //     dispatch(setUserAllAddress(result))
  //     // 如果删除的是当前选中的地址，清空选中状态
  //     if (String(addressToDelete.address_id) === selectedAddressId) {
  //       dispatch(setReturnAddressId(null))
  //     }
  //     await sleep(500)
  //     toast.show({
  //       icon: 'success',
  //       content: getI18nString('delete_address_success'),
  //     })
  //   }
  //   setIsDeleteModalOpen(false)
  //   setAddressToDelete(null)
  // })

  return (
    <>
      <CustomDrawer
        title={getI18nString('my_sending_address')}
        onClose={onClose}
        open={visible}
        footer={
          <div className="flex items-center gap-base-16">
            <Button style={{ width: '144px' }} onClick={onClose}>
              {getI18nString('previous')}
            </Button>
            <Button type="primary" style={{ flex: 1 }} onClick={handleConfirmAddress}>
              {getI18nString('confirm')}
            </Button>
          </div>
        }>
        <LocalToastProvider
          containerRef={containerRef}
          position="center"
          contentClassName="gap-[8px] px-[16px] py-[12px]"
          textClassName="text-[16px] leading-[1.4]"
          iconWidth={22}
          iconHeight={22}>
          <div ref={containerRef} className="relative pt-base-24">
            <div className="space-y-[16px]">
              {/* 地址列表 */}
              {userAllAddress.map((address) => (
                <div
                  key={address.address_id}
                  className={`group relative cursor-pointer rounded-base border px-base py-base-16 transition-colors ${
                    tempSelectedAddressId === String(address.address_id)
                      ? 'border-primary'
                      : 'border-[#E1E1E4] hover:border-primary'
                  }`}
                  onClick={() => handleSelectAddress(address)}>
                  <div className="flex gap-base-12">
                    <Location
                      fill={
                        tempSelectedAddressId === String(address.address_id) ? '#DA291C' : '#BBBBBD'
                      }
                    />
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-[4px]">
                        <span className="font-miSansDemiBold450 text-[18px] leading-none text-[#0F0F0F]">
                          {address.receive_name}
                        </span>
                        <span className="font-miSansDemiBold450 text-[18px] leading-none text-[#0F0F0F]">
                          {address.receive_phone}
                        </span>
                        {address.is_default && (
                          <span className="rounded-[4px] bg-primary p-[4px] font-miSansRegular330 text-[12px] leading-[100%] text-white">
                            {getI18nString('default_address')}
                          </span>
                        )}
                      </div>
                      <div className="mt-base font-miSansRegular330 text-[16px] leading-none text-[#444446]">
                        {address.province} {address.city} {address.county} {address.street}
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮组 - 悬停时显示 */}
                  {/* <div className="absolute bottom-0 right-0 hidden items-center gap-2 rounded-br-base rounded-tl-base bg-gray-base px-base py-[4px] group-hover:flex">
                  <button
                    className="hover:text-primary"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleOpenDeleteModal(address)
                    }}>
                    删除
                  </button>
                  <span className="text-gray-3">|</span>
                  <button
                    className="hover:text-primary"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleOpenEditModal(address)
                    }}>
                    编辑
                  </button>
                </div> */}
                </div>
              ))}

              {/* 添加地址按钮 */}
              <div
                className="flex h-[92px] cursor-pointer items-center justify-center rounded-base border border-dashed border-gray-300 hover:border-primary"
                onClick={handleOpenAddModal}>
                <div className="flex items-center gap-base text-[#0F0F0F]">
                  <AddIcon />
                  <div className="leading-[140%]">{getI18nString('add_shipping_address')}</div>
                </div>
              </div>
            </div>
          </div>
        </LocalToastProvider>
      </CustomDrawer>

      {/* 地址编辑/添加弹窗 */}
      <AddressEditDrawer
        visible={isAddressModalOpen}
        editingAddress={editingAddress}
        onClose={handleCloseAddressModal}
        onSubmit={editingAddress ? handleUpdateAddress : handleAddAddress}
        onGoBack={() => {
          // 重新打开地址管理抽屉
          if (onOpen) {
            onOpen()
          }
        }}
      />

      {/* 删除确认模态框 */}
      {/* <Modal
        isOpen={isDeleteModalOpen}
        title={getI18nString('delete_address_title')}
        okText={getI18nString('confirm')}
        cancelText={getI18nString('thinking')}
        width={400}
        onConfirm={handleConfirmDelete}
        onClose={() => {
          setIsDeleteModalOpen(false)
          setAddressToDelete(null)
        }}>
        <p>{getI18nString('confirm_delete_address')}</p>
      </Modal> */}
    </>
  )
}

// 导出内部组件作为主组件
export default AddressManagementContent
