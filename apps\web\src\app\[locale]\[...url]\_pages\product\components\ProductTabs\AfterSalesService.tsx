import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { cn, type ProductDetailsData } from '@ninebot/core'
import { RenderHtml } from '@ninebot/core/src/components'

import { IconArrow } from '@/components'
interface AfterSalesServiceProps {
  productData: ProductDetailsData
}

const AfterSalesService = ({ productData }: AfterSalesServiceProps) => {
  const afterSalesService =
    productData.after_sales_instructions || productData.mb_after_sales_instructions || ''
  const [isExpanded, setIsExpanded] = useState(false)
  const getI18nString = useTranslations('Common')
  if (!afterSalesService) return null

  return (
    <>
      <div
        className="flex cursor-pointer items-center justify-between"
        onClick={() => setIsExpanded(!isExpanded)}>
        <h3 className="text-[28px] leading-[1.2]">{getI18nString('product_after_sale')}</h3>
        <div className="flex items-center gap-[6px] font-miSansDemiBold450 text-[16px] leading-[20px]">
          <span>{isExpanded ? '收起' : '展开'}</span>
          <IconArrow size={20} rotate={isExpanded ? 180 : 0} />
        </div>
      </div>

      <div
        className={cn(`mt-4 max-h-0 overflow-y-hidden px-8 transition-all duration-300`, {
          'max-h-[1500px]': isExpanded,
        })}>
        <RenderHtml content={`<div style="color: #000;">${afterSalesService}</div>`} />
      </div>
    </>
  )
}

export default AfterSalesService
