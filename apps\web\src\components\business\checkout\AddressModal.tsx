import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  type Address,
  mergeStyles,
  resolveCatchMessage,
  TCatchMessage,
  useLazyGetProvinceCityCountyQuery,
  useToastContext,
  useUserAddress,
} from '@ninebot/core'
import { Checkbox, Form, Input, Space } from 'antd'

import { Icon<PERSON>rrow, Modal } from '@/components'

interface AddressModalProps {
  open: boolean
  editingAddress?: Address | null
  onCancel: () => void
  onOk: (values: Address) => void
}

interface ApiAreaItem {
  default_name: string
  region_id?: string
  city_id?: string
  district_id?: string
}

interface AreaItem {
  label: string
  id: string
}

const TipIcon = () => {
  return (
    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6 1.5C3.23857 1.5 0.999999 3.73858 1 6.5C1 9.26143 3.23857 11.5 6 11.5C8.76143 11.5 11 9.26143 11 6.5C11 3.73857 8.76142 1.5 6 1.5Z"
        stroke="#DA291C"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 9.05078L6.5 8.55078L6 8.05078L5.5 8.55078L5.5 9.05078L6.5 9.05078Z"
        fill="#DA291C"
      />
      <path
        d="M5.5 3.98118L5.5 7.07031L6 7.57031L6.5 7.07031L6.5 3.98118L5.5 3.98118Z"
        fill="#DA291C"
      />
    </svg>
  )
}

const DEFAULT_PROVINCE = { label: '省份', id: '' }
const DEFAULT_CITY = { label: '城市', id: '' }
const DEFAULT_DISTRICT = { label: '县区', id: '' }

export default function AddressModal({ open, editingAddress, onCancel, onOk }: AddressModalProps) {
  const [form] = Form.useForm<Address>()

  const getI18nString = useTranslations('Web')
  const toast = useToastContext()
  const [getAddressData] = useLazyGetProvinceCityCountyQuery()

  const [activeTab, setActiveTab] = useState<'province' | 'city' | 'district'>('province')

  const [activeProvince, setActiveProvince] = useState<AreaItem>(DEFAULT_PROVINCE)
  const [activeCity, setActiveCity] = useState<AreaItem>(DEFAULT_CITY)
  const [activeDistrict, setActiveDistrict] = useState<AreaItem>(DEFAULT_DISTRICT)

  const [provinces, setProvinces] = useState<AreaItem[]>([])
  const [cities, setCities] = useState<AreaItem[]>([])
  const [districts, setDistricts] = useState<AreaItem[]>([])

  const [isDefaultSet, setIsDefaultSet] = useState(true) // 是否设置默认值

  const [addressSelect, setAddressSelect] = useState(false)

  // 地址选择器验证状态
  const [addressValidateStatus, setAddressValidateStatus] = useState<'error' | ''>('')

  const { userAddressDefault } = useUserAddress()

  /**
   * 获取地址数据
   */
  const fetchAddressData = useCallback(
    ({ id, type }: { id?: string; type: 'region' | 'city' }) => {
      getAddressData({ FilterValue: id, type })
        .unwrap()
        .then((res) => {
          if (!res?.province_city_county_search) return
          const parsedData = JSON.parse(res.province_city_county_search) as ApiAreaItem[]
          if (id && type === 'city') {
            const formatData = parsedData.map((item) => ({
              id: item.district_id || '',
              label: item.default_name,
            }))
            setDistricts(formatData)
          } else if (id && type === 'region') {
            const formatData = parsedData.map((item) => ({
              id: item.city_id || '',
              label: item.default_name,
            }))
            setCities(formatData)
          } else {
            const formatData = parsedData.map((item) => ({
              id: item.region_id || '',
              label: item.default_name,
            }))
            setProvinces(formatData)
          }
        })
        .catch((error) => {
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error as TCatchMessage) as string,
          })
        })
    },
    [getAddressData, toast],
  )

  /**
   * 获取省份
   */
  useEffect(() => {
    fetchAddressData({ type: 'region' })
  }, [fetchAddressData])

  /**
   * 获取城市
   */
  useEffect(() => {
    if (activeProvince.id) {
      fetchAddressData({ id: activeProvince.id, type: 'region' })
    } else {
      // 清空城市列表
      setCities([])
    }
  }, [fetchAddressData, activeProvince.id])

  /**
   * 获取区
   */
  useEffect(() => {
    if (activeCity.id) {
      fetchAddressData({ id: activeCity.id, type: 'city' })
    } else {
      // 清空区县列表
      setDistricts([])
    }
  }, [fetchAddressData, activeCity.id])

  /**
   * 设置默认省份
   */
  useEffect(() => {
    if (provinces.length > 0 && editingAddress?.province) {
      const defaultProvince = provinces.find(
        (province) => province.label === editingAddress.province,
      )
      if (defaultProvince) {
        setActiveProvince(defaultProvince)
      }
    }
  }, [provinces, editingAddress?.province])

  /**
   * 设置默认城市
   */
  useEffect(() => {
    if (cities.length > 0 && editingAddress?.city) {
      const defaultCity = cities.find((city) => city.label === editingAddress.city)
      if (defaultCity) {
        setActiveCity(defaultCity)
      }
    }
  }, [cities, editingAddress?.city])

  /**
   * 设置默认区
   */
  useEffect(() => {
    if (districts.length > 0 && editingAddress?.county) {
      const defaultDistrict = districts.find((district) => district.label === editingAddress.county)
      if (defaultDistrict) {
        setActiveDistrict(defaultDistrict)
      }
    }
  }, [districts, editingAddress?.county])

  /**
   * 地址列表
   */
  const addressList = useMemo(() => {
    switch (activeTab) {
      case 'province':
        return provinces
      case 'city':
        return cities
      case 'district':
        return districts
      default:
        return []
    }
  }, [activeTab, provinces, cities, districts])

  /**
   * 当前选中的item
   */
  const currentAddress = useMemo(() => {
    switch (activeTab) {
      case 'province':
        return activeProvince
      case 'city':
        return activeCity
      case 'district':
        return activeDistrict
      default:
        return DEFAULT_PROVINCE
    }
  }, [activeTab, activeProvince, activeCity, activeDistrict])

  /**
   * 选择地址
   */
  const handleAddressSelect = (address: AreaItem) => {
    setIsDefaultSet(false)
    // 清除验证错误状态
    setAddressValidateStatus('')
    // 清除表单字段的验证错误信息
    form.setFields([
      {
        name: 'province',
        errors: [],
      },
    ])
    switch (activeTab) {
      case 'province':
        setActiveProvince(address)
        setActiveCity(DEFAULT_CITY)
        setActiveDistrict(DEFAULT_DISTRICT)
        setActiveTab('city')
        break
      case 'city':
        setActiveCity(address)
        setActiveDistrict(DEFAULT_DISTRICT)
        setActiveTab('district')
        break
      case 'district':
        setActiveDistrict(address)
        setAddressSelect(false)
        break
    }
  }

  /**
   * 打开地址下拉
   */
  const handleAddressDropdown = (tab: 'province' | 'city' | 'district') => {
    // 判断是否可以选择该级别
    if (tab === 'city' && activeProvince.id === '') {
      return // 如果省份未选中，不能选择城市
    }
    if (tab === 'district' && (activeProvince.id === '' || activeCity.id === '')) {
      return // 如果省份或城市未选中，不能选择区县
    }
    setActiveTab(tab)
    setAddressSelect(true)
  }

  useEffect(() => {
    if (open) {
      if (editingAddress) {
        form.setFieldsValue(editingAddress)
      } else {
        form.resetFields()
        form.setFieldsValue({
          is_default: true,
        })
      }
    }
  }, [editingAddress, form, open])

  const handleFinish = async (values: Address) => {
    if (!isDefaultSet) {
      await onOk({
        ...values,
        address_id: editingAddress?.address_id || 0,
        tag: '',
        province: activeProvince.label,
        city: activeCity.label,
        county: activeDistrict.label,
      })
    } else {
      onCancel()
    }

    form.resetFields()
    handleClearArea()
  }

  const handleClearArea = () => {
    setActiveProvince(DEFAULT_PROVINCE)
    setActiveCity(DEFAULT_CITY)
    setActiveDistrict(DEFAULT_DISTRICT)
    setActiveTab('province')
    setAddressSelect(false)
    setAddressValidateStatus('')
  }

  /**
   * 默认地址能否切换
   */
  const canSwitchDefaultAddress = useMemo(() => {
    if (userAddressDefault && userAddressDefault.address_id === editingAddress?.address_id) {
      return true
    }
    return false
  }, [userAddressDefault, editingAddress])

  return (
    <Modal
      title={
        editingAddress
          ? getI18nString('edit_shipping_address')
          : getI18nString('add_new_shipping_address')
      }
      isOpen={open}
      okText={getI18nString('save_address')}
      onConfirm={() => form.submit()}
      onClose={() => {
        form.resetFields()
        onCancel()
        handleClearArea()
      }}
      width={600}
      styles={{
        footer: {
          margin: '32px 0 8px',
        },
      }}>
      <Form
        className="address-form"
        form={form}
        onFinish={handleFinish}
        layout="vertical"
        onValuesChange={() => setIsDefaultSet(false)}
        initialValues={{
          is_default: true,
        }}>
        <Form.Item
          className="table-item"
          name="receive_name"
          label=" "
          colon={false}
          layout="horizontal"
          rules={[
            {
              required: true,
              message: (
                <div className="flex items-center gap-[4px]">
                  <TipIcon />
                  {getI18nString('consignee_tip')}
                </div>
              ),
            },
          ]}>
          <Input placeholder={getI18nString('consignee_tip')} style={{ padding: '12px' }} />
        </Form.Item>

        <Space.Compact block className="gap-base-12">
          <div
            className="flex items-center justify-center rounded-base border border-[#DCDCDC] bg-[#F9F9F9] px-base-12 text-[#0F0F0F]"
            style={{ height: '48px' }}>
            {getI18nString('cn_phone')}
          </div>
          <Form.Item
            style={{ flex: 1 }}
            name="receive_phone"
            label=" "
            colon={false}
            layout="horizontal"
            rules={[
              {
                required: true,
                message: (
                  <div className="flex items-center gap-[4px]">
                    <TipIcon />
                    {getI18nString('phone_tip')}
                  </div>
                ),
              },
              {
                pattern: /^1[3-9]\d{9}$/,
                message: (
                  <div className="flex items-center gap-[4px]">
                    <TipIcon />
                    {getI18nString('error_phone')}
                  </div>
                ),
              },
            ]}>
            <Input
              style={{ padding: '12px', borderRadius: '8px' }}
              maxLength={11}
              type="tel"
              inputMode="tel"
              placeholder={getI18nString('phone_tip')}
              onInput={(e) => {
                // 只允许输入数字，并限制长度为11位
                const target = e.target as HTMLInputElement
                target.value = target.value.replace(/[^\d]/g, '').slice(0, 11)
              }}
            />
          </Form.Item>
        </Space.Compact>

        <Form.Item
          name="province"
          label=" "
          colon={false}
          layout="horizontal"
          className="address-select"
          rules={[
            {
              required: true,
              validator: async () => {
                if (!activeProvince.id || !activeCity.id || !activeDistrict.id) {
                  setAddressValidateStatus('error')
                  return Promise.reject({
                    message: (
                      <div className="flex items-center gap-[4px]">
                        <TipIcon />
                        {getI18nString('choose_area')}
                      </div>
                    ),
                  })
                }
                setAddressValidateStatus('')
                // 清除表单字段的验证错误信息
                form.setFields([
                  {
                    name: 'province',
                    errors: [],
                  },
                ])
                return Promise.resolve()
              },
            },
          ]}>
          <div
            className={mergeStyles([
              'relative flex h-[48px] w-full flex-col rounded-base border', // 根据验证状态设置边框颜色
              addressValidateStatus === 'error' ? 'border-[#E34D59]' : 'border-[#d9d9d9]',
            ])}>
            <div
              className={
                'flex h-[48px] w-full cursor-pointer items-center justify-between px-[12px]'
              }
              onClick={() => {
                setActiveTab('province')
                setAddressSelect((pre) => !pre)
              }}>
              {activeProvince.label && (
                <div className="flex h-[48px] flex-row items-center text-[#00000066]">
                  <div
                    onClick={(e) => {
                      e.stopPropagation()
                      handleAddressDropdown('province')
                    }}
                    className={mergeStyles([
                      addressSelect && currentAddress.label === activeProvince.label
                        ? 'text-[#DA291C]'
                        : 'text-[#000000]',
                      'mr-[4px] cursor-pointer',
                    ])}>
                    {activeProvince.label}
                  </div>
                  <div className={mergeStyles(activeProvince.label !== '省份' && 'text-[#000000]')}>
                    |
                  </div>
                  {activeCity.label && (
                    <>
                      <div
                        onClick={(e) => {
                          e.stopPropagation()
                          handleAddressDropdown('city')
                        }}
                        className={mergeStyles([
                          addressSelect &&
                          currentAddress.label === activeCity.label &&
                          activeCity.label !== '城市'
                            ? 'text-[#DA291C]'
                            : activeCity.label !== '城市' && 'text-[#000000]',
                          'mx-[4px]',
                          activeProvince.id ? 'cursor-pointer' : 'opacity-50',
                        ])}>
                        {activeCity.label}
                      </div>
                      <div className={mergeStyles(activeCity.label !== '城市' && 'text-[#000000]')}>
                        |
                      </div>
                    </>
                  )}
                  {activeDistrict.label && (
                    <div
                      onClick={(e) => {
                        e.stopPropagation()
                        handleAddressDropdown('district')
                      }}
                      className={mergeStyles([
                        addressSelect &&
                        currentAddress.label === activeDistrict.label &&
                        activeDistrict.label !== '县区'
                          ? 'text-[#DA291C]'
                          : activeDistrict.label !== '县区' && 'text-[#000000]',
                        'ml-[4px]',
                        activeProvince.id && activeCity.id ? 'cursor-pointer' : 'opacity-50',
                      ])}>
                      {activeDistrict.label}
                    </div>
                  )}
                </div>
              )}

              <IconArrow color="#00000066" rotate={addressSelect ? 180 : 0} />
            </div>

            {addressSelect && addressList.length > 0 && (
              <div
                className="absolute left-0 top-[56px] z-10 w-full rounded-base bg-white p-[24px]"
                style={{
                  boxShadow: '0px 0px 32px 0px rgba(0, 0, 0, 0.1)',
                }}>
                <div className="flex flex-wrap gap-x-16">
                  {addressList.map((item) => (
                    <div
                      key={item.id}
                      onClick={() => handleAddressSelect(item)}
                      className={mergeStyles([
                        'w-[60px] cursor-pointer truncate font-miSansMedium380 text-[14px] leading-[30px] hover:text-primary',
                        `${currentAddress.id === item.id ? 'text-[#DA291C]' : 'text-[#000000]'}`,
                      ])}>
                      {item.label}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Form.Item>

        <Form.Item
          name="street"
          label=" "
          colon={false}
          layout="horizontal"
          rules={[
            {
              required: true,
              message: (
                <div className="flex items-center gap-[4px]">
                  <TipIcon />
                  {getI18nString('detailed_address_tip')}
                </div>
              ),
            },
          ]}>
          <Input placeholder={getI18nString('address_placeholder')} style={{ padding: '12px' }} />
        </Form.Item>

        <Form.Item name="is_default" valuePropName="checked">
          <Checkbox disabled={canSwitchDefaultAddress}>
            <span className="font-miSansRegular330 text-[14px] leading-[140%] text-[#0F0F0F]">
              {getI18nString('set_default_shipping_address')}
            </span>
          </Checkbox>
        </Form.Item>
      </Form>
    </Modal>
  )
}
